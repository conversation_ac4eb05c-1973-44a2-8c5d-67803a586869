# ReYoutube Web

YouTube频道和视频数据分析平台的Web端。

## 数据库连接配置

为了优化数据库连接性能，请在`.env`文件中配置以下参数：

```DATABASE_URL='postgresql://user:password@host:port/dbname?connection_limit=20&pool_timeout=30&statement_timeout=60000&idle_in_transaction_session_timeout=60000'
```

参数说明：
- `connection_limit`: 连接池大小上限
- `pool_timeout`: 从连接池获取连接的超时时间(秒)
- `statement_timeout`: SQL语句执行超时时间(毫秒)
- `idle_in_transaction_session_timeout`: 事务空闲超时时间(毫秒)

## 开发指南

项目使用Next.js构建，数据库操作通过Prisma ORM实现。

### 运行开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000)。

### 数据库操作

- 同步数据库结构: `pnpm run db:sync`
- 启动Prisma Studio: `pnpm run db:studio`
- 测试数据库连接: `pnpm run test:db`

## @用户名访问功能

我们实现了一个强大的@用户名访问功能，允许用户通过简化的URL格式访问频道：

- 用户可以直接访问 `http://localhost:3000/@username` 格式的URL
- 系统会自动搜索数据库中对应的频道
- 如果数据库中找不到该频道，会尝试从外部API获取数据
- 支持友好的错误处理和用户体验

### 实现细节

1. **中间件处理**：
   - 捕获 `/@username` 格式的URL
   - 将请求重写到API路由处理

2. **数据查询优化**：
   - 支持同时查询带@和不带@的用户名格式
   - 提供外部API作为数据源备份

3. **错误处理**：
   - 完善的错误日志记录
   - 用户友好的错误页面
   - 自动重定向逻辑

### 使用方法

只需访问 `http://localhost:3000/@username` 即可使用此功能。系统将：

- 首先在数据库中查找对应的频道
- 如果找不到，则调用外部API获取数据
- 根据查询结果，将用户重定向到相应页面

### 开发者注意事项

- 此功能使用NextJS的动态路由和中间件
- 确保正确处理异步参数并使用await获取参数
- 日志记录包含关键调试信息

## TODO：
第一期：
1. 详情页，登录态和未登录下一致
2. 付费功能后期

1. 视频查询优化，加载速度，两边查询修复
2. 移动端适配
3. 优先搞定英文版本
4. 如何查看postgre的sql性能-Done
    - CREATE INDEX idx_ytb_videos_channel_published ON ytb_videos(channel_id, published_at DESC) WHERE deleted_at IS NULL;

    SELECT "public"."ytb_videos"."id", "public"."ytb_videos"."title", "public"."ytb_videos"."thumbnail_url", "public"."ytb_videos"."published_at", "public"."ytb_videos"."view_count", "public"."ytb_videos"."like_count", "public"."ytb_videos"."comment_count" FROM "public"."ytb_videos" WHERE ("public"."ytb_videos"."channel_id" = $1 AND "public"."ytb_videos"."deleted_at" IS NULL) ORDER BY "public"."ytb_videos"."published_at" DESC LIMIT $2 OFFSET $3

5. 国际化
6. 首页优化样式营销
7. 头部和底部
8. 登录功能验证
9. 登录后的页面验证
10. 

## 站点地图功能

本项目使用 `next-sitemap` 自动生成站点地图和robots.txt文件，以便搜索引擎更好地索引网站内容。

### 功能特点

- 使用 `next-sitemap` 包生成完整的站点地图
- 支持静态和动态路由的站点地图生成
- 自动包含多语言路由配置
- 包含优先级和更新频率设置
- 自动生成指向站点地图的robots.txt文件

### 如何配置

1. 在`.env`文件中设置您的网站基础URL：
   ```
   NEXT_PUBLIC_WEB_URL=https://www.reyoutube.com
   ```

2. 自定义站点地图:
   - 编辑`next-sitemap.config.js`文件，添加或修改您网站的路由
   - 通过`additionalPaths`配置添加动态路由

3. 构建和生成站点地图:
   ```bash
   # 构建项目（包含postbuild钩子自动生成站点地图）
   pnpm run build
   
   # 或单独生成站点地图
   pnpm run sitemap
   ```

### 访问站点地图

构建并部署项目后，您可以通过以下URL访问:
- 站点地图索引: `https://www.reyoutube.com/sitemap.xml`
- 详细站点地图: `https://www.reyoutube.com/sitemap-0.xml`
- Robots文件: `https://www.reyoutube.com/robots.txt`

### 添加新页面到站点地图

当添加新页面时，您需要更新`next-sitemap.config.js`中的`additionalPaths`配置，确保新页面被包含在站点地图中。 