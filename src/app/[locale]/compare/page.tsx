"use client"

import { useState } from "react"
import { Plus, Trash2, <PERSON><PERSON><PERSON>2, Users, Eye, ThumbsUp, MessageSquare, Share2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Mock data structure
interface ChannelData {
  id: string;
  name: string;
  avatar: string;
  subscribers: string;
  views: string;
  likes: string;
  comments: string;
  shares: string;
  category: string;
}

export default function ComparePage() {
  const [channels, setChannels] = useState<ChannelData[]>([
    // Initial mock data
    { id: '1', name: 'Tech Explained', avatar: '/placeholder-user.jpg', subscribers: '1.5M', views: '250M', likes: '12M', comments: '500K', shares: '1M', category: '科技' },
    { id: '2', name: 'Gadget Reviews', avatar: '/placeholder-user.jpg', subscribers: '1.2M', views: '180M', likes: '9M', comments: '350K', shares: '800K', category: '科技' },
  ]);
  const [searchTerm, setSearchTerm] = useState('');

  const addChannel = () => {
    if (channels.length < 4 && searchTerm) { // Limit to 4 channels for comparison
      // In a real app, you'd fetch channel data based on searchTerm
      const newChannel: ChannelData = {
        id: Date.now().toString(),
        name: searchTerm,
        avatar: '/placeholder-user.jpg',
        subscribers: `${(Math.random() * 2 + 0.5).toFixed(1)}M`,
        views: `${Math.floor(Math.random() * 300 + 50)}M`,
        likes: `${Math.floor(Math.random() * 15 + 1)}M`,
        comments: `${Math.floor(Math.random() * 600 + 100)}K`,
        shares: `${(Math.random() * 1.5 + 0.2).toFixed(1)}M`,
        category: '综合'
      };
      setChannels([...channels, newChannel]);
      setSearchTerm('');
    }
  };

  const removeChannel = (id: string) => {
    setChannels(channels.filter(channel => channel.id !== id));
  };

  return (
    // Use bg-background for the main container
    <div className="min-h-screen bg-background">
      {/* Top Bar already uses bg-background from layout, ensure content below respects theme */}
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">频道对比</h1>

        {/* Add Channel Section - Use bg-card */}
        <Card className="mb-8 bg-card">
          <CardContent className="p-6 flex items-center space-x-4">
            <Input
              placeholder="输入频道名称或链接添加对比..."
              className="flex-grow"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addChannel()}
            />
            <Button onClick={addChannel} disabled={channels.length >= 4 || !searchTerm}>
              <Plus className="mr-2 h-4 w-4" /> 添加对比
            </Button>
          </CardContent>
        </Card>

        {/* Comparison Grid */}
        <div className={`grid gap-6 ${channels.length > 0 ? `grid-cols-1 md:grid-cols-${Math.min(channels.length, 4)}` : ''}`}>
          {channels.map((channel) => (
            // Use bg-card for each channel card
            <Card key={channel.id} className="relative bg-card overflow-hidden">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
                onClick={() => removeChannel(channel.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <CardHeader className="flex flex-row items-center space-x-4 pb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={channel.avatar} alt={channel.name} />
                  <AvatarFallback>{channel.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-lg">{channel.name}</CardTitle>
                  {/* Use text-muted-foreground */}
                  <Badge variant="secondary" className="mt-1">{channel.category}</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Use text-muted-foreground for labels */}
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><Users className="mr-2 h-4 w-4" /> 订阅数</span>
                  <span className="font-medium">{channel.subscribers}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><Eye className="mr-2 h-4 w-4" /> 总观看</span>
                  <span className="font-medium">{channel.views}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><ThumbsUp className="mr-2 h-4 w-4" /> 点赞数</span>
                  <span className="font-medium">{channel.likes}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><MessageSquare className="mr-2 h-4 w-4" /> 评论数</span>
                  <span className="font-medium">{channel.comments}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><Share2 className="mr-2 h-4 w-4" /> 分享数</span>
                  <span className="font-medium">{channel.shares}</span>
                </div>
              </CardContent>
            </Card>
          ))}

          {channels.length === 0 && (
            // Use text-muted-foreground for placeholder text
            <div className="text-center py-12 text-muted-foreground col-span-full">
              <BarChart2 className="mx-auto h-12 w-12 mb-4" />
              <p>请添加至少一个频道以开始对比。</p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
