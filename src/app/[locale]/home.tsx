"use client"

import type React from "react"
import { useState, useEffect, useRef, useCallback } from "react"
import { Features } from "@/components/sections/Features"
// Import useParams
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { Search, TrendingUp, BarChart3, Users, Loader2, Plus, SlidersHorizontal, ChevronDown, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Popover, PopoverContent, PopoverAnchor } from "@/components/ui/popover"
import {
  Command, CommandItem,
  CommandList
} from "@/components/ui/command"
import debounce from 'lodash/debounce'
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Play } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Di<PERSON><PERSON>ooter,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { useTranslations } from 'next-intl'

// Safari兼容性样式
const safariStyles = `
  @supports (-webkit-touch-callout: none) {
    .min-h-screen {
      /* 修复Safari下高度计算问题 */
      min-height: -webkit-fill-available;
    }
    
    .search-section {
      /* 确保Safari下搜索区域正确显示 */
      position: relative;
      z-index: 10;
    }
  }
`

// Define the structure for suggestions
interface ChannelSuggestion {
  channelId: string;
  name: string;
  title: string;
  thumbnailUrl?: string | null;
  customUrl?: string | null;
  subscriberCount?: number | null;
  videoCount?: number | null;
}

// Helper function to format subscriber count
function formatSubscribers(count: number | null | undefined): string | null {
  if (count === null || count === undefined || count < 0) { // Also handle potential negative counts if needed
    return null;
  }
  if (count >= 1000000) {
    // Keep one decimal place only if it's not .0
    const millions = (count / 1000000);
    return (millions % 1 === 0 ? millions.toFixed(0) : millions.toFixed(1)) + 'M';

  }
  if (count >= 1000) {
     // Keep one decimal place only if it's not .0
     const thousands = (count / 1000);
     return (thousands % 1 === 0 ? thousands.toFixed(0) : thousands.toFixed(1)) + 'K';
  }
  return count.toString();
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL

export default function HomePage() {
  const t = useTranslations('home')
  const router = useRouter()
  // Use useParams to get locale
  const params = useParams();
  const locale = params.locale as string; // Ensure locale is treated as string
  const [searchQuery, setSearchQuery] = useState("")
  const [suggestions, setSuggestions] = useState<ChannelSuggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [showTip, setShowTip] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const anchorRef = useRef<HTMLDivElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)
  const [contentWidth, setContentWidth] = useState<number | undefined>(undefined)
  const [searchResults, setSearchResults] = useState<ChannelSuggestion[]>([])
  const [error, setError] = useState<string | null>(null)
  const [isTrackDialogOpen, setIsTrackDialogOpen] = useState(false)
  const [trackChannelInput, setTrackChannelInput] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  
  // 检测是否为Safari浏览器
  const [isSafari, setIsSafari] = useState(false)
  
  useEffect(() => {
    // 检测Safari浏览器
    const ua = navigator.userAgent.toLowerCase();
    setIsSafari(/^((?!chrome|android).)*safari/i.test(ua));
  }, []);

  // Define the debounced function type
  type FetchSuggestionsType = (query: string) => Promise<ChannelSuggestion[]>

  // Create the debounced function
  const debouncedFetch = useCallback(
    debounce<FetchSuggestionsType>(async (query: string) => {
      const trimmedQuery = query.trim()
      if (trimmedQuery.length < 3) {
        setSuggestions([])
        setIsLoading(false)
        setIsDropdownOpen(false)
        setShowTip(true)
        return []
      }
      setIsLoading(true)
      try {
        const response = await fetch(`/api/channels/search?query=${encodeURIComponent(trimmedQuery)}`)
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        const data: ChannelSuggestion[] = await response.json()
        setSuggestions(data)
        // 无论是否有结果都保持下拉列表显示
        // setIsDropdownOpen(true)
        return data
      } catch (error) {
        console.error("Failed to fetch suggestions:", error)
        setSuggestions([])
        setIsDropdownOpen(true)
        return []
      } finally {
        setIsLoading(false)
      }
    }, 300),
    []
  )

  useEffect(() => {
    if (searchQuery) {
      debouncedFetch(searchQuery)
    } else {
      setSuggestions([])
      setIsLoading(false)
      setIsDropdownOpen(false)
      debouncedFetch.cancel()
    }
    return () => {
      debouncedFetch.cancel()
    }
  }, [searchQuery, debouncedFetch])

  // Effect to measure anchor width when dropdown opens
  useEffect(() => {
    if (isDropdownOpen && anchorRef.current) {
      setContentWidth(anchorRef.current.offsetWidth);
    }
  }, [isDropdownOpen]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!searchQuery.trim()) return

    // 立即关闭下拉列表
    setIsDropdownOpen(false)
    setIsLoading(true)
    setError(null)
    
    try {
      const results = await debouncedFetch(searchQuery)
      if (results) {
        setSearchResults(results)
        // 调整滚动位置
        const searchSection = document.querySelector('.search-section')
        if (searchSection) {
          const headerHeight = 80 // 预估的header高度
          const offset = searchSection.getBoundingClientRect().top + window.pageYOffset - headerHeight
          window.scrollTo({
            top: offset,
            behavior: 'smooth'
          })
        }
      }
    } catch (err) {
      setError(t('searchError'))
      console.error('Search error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelectSuggestion = (suggestion: ChannelSuggestion) => {
    // 关闭下拉列表
    setIsDropdownOpen(false)
    
    // 直接导航到频道详情页
    router.push(`/${locale}/channel/${encodeURIComponent(suggestion.channelId)}`)
  }

  // Function to handle "See all results" click (uses current query)
  const handleSeeAllResults = () => {
    const formEvent = new Event('submit') as unknown as React.FormEvent
    handleSearch(formEvent)
  }

  const handleTrackChannel = async () => {
    if (!trackChannelInput.trim()) return
    
    setIsSubmitting(true)
    try {
      const response = await fetch(`${API_BASE_URL}/api/channel/videos/history-recent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel_url: trackChannelInput.trim()
        })
      })

      if (!response.ok) {
        let errorMessage = t('submitFailed');
        try {
          const errorData = await response.json();
          console.log("errorData", errorData);
          // 尝试从 detail 数组中提取第一个错误消息
          if (errorData && Array.isArray(errorData.detail) && errorData.detail.length > 0 && errorData.detail[0].msg) {
            errorMessage = errorData.detail[0].msg;
          }
        } catch (e) {
          // 如果解析 JSON 失败或结构不符，则使用默认错误消息
          console.error("Failed to parse error response:", e);
        }
        throw new Error(errorMessage);
      }else{
        console.log("提交成功:",response.text())
      }

      toast({
        title: t('channelAddSuccess'),
        description: t('channelAddSuccessDesc'),
        duration: 5000,
      })

      setIsTrackDialogOpen(false)
      setTrackChannelInput("")
    } catch (error) {
      console.log("error", error);

      toast({
        variant: "destructive",
        title: t('addFailed'),
        description: error instanceof Error ? error.message : t('checkChannelURL'),
        duration: 10000,
      });

      // 注意：这里的 setTimeout 可能会导致对话框在 toast 完全显示之前关闭，
      // 但为了确保对话框最终关闭，暂时保留。如果需要更精确的控制，可以考虑其他方式。
      setTimeout(() => {
        setIsTrackDialogOpen(false);
        setTrackChannelInput("");
      }, 1000);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      {/* 添加Safari兼容性样式 */}
      <style jsx global>{safariStyles}</style>
      
      <div className="min-h-screen flex flex-col">
        {/* Header (Commented out section - no changes needed here unless uncommented) */}
        {/* <header className="bg-white border-b">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center">
              <Database className="h-6 w-6 text-emerald-500 mr-2" />
              <span className="font-bold text-xl">DataFlow Analytics</span>
            </div>
            <div className="flex items-center space-x-4">
              <nav className="hidden md:flex space-x-6">
                <Link href={`/${locale}/dashboard`} className="text-slate-600 hover:text-slate-900">
                  仪表盘
                </Link>
                <Link href={`/${locale}/tasks`} className="text-slate-600 hover:text-slate-900">
                  数据任务
                </Link>
                <Link href={`/${locale}/reports`} className="text-slate-600 hover:text-slate-900">
                  报表
                </Link>
              </nav>
              <Button asChild variant="outline">
                <Link href={`/${locale}/login`}>登录</Link>
              </Button>
              <Button asChild>
                <Link href={`/${locale}/login`}>注册</Link>
              </Button>
            </div>
          </div>
        </header> */}

        {/* Hero Section with Search */}
        {/* 使用 bg-background 和 bg-card/80 (或类似) 替代硬编码的 slate/white */}
        <section className="bg-gradient-to-b from-background to-card/80 py-10 md:py-20 flex-grow dark:to-background/80">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="text-center mb-8 md:mb-12">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4">{t('title')}</h1>
              {/* 使用 text-muted-foreground 替代 text-slate-600 */}
              <p className="text-lg md:text-xl text-muted-foreground mb-6 md:mb-8 max-w-3xl mx-auto px-2">
                {t('subtitle')}
              </p>

              {/* Search Form with Popover - Mobile-optimized */}
              <div className="search-section px-2 sm:px-4">
                <form onSubmit={handleSearch} className="max-w-2xl mx-auto relative">
                  <Popover 
                    open={isDropdownOpen} 
                    onOpenChange={(open) => {
                      if (!open && document.activeElement === document.body) {
                        setIsDropdownOpen(false)
                      }
                    }}
                  >
                    <PopoverAnchor asChild>
                      <div 
                        className="flex flex-col sm:flex-row gap-2 sm:gap-0" 
                        ref={anchorRef}
                        style={isSafari ? { position: 'relative', zIndex: 1 } : undefined}
                      >
                        <div className="relative flex-grow">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground z-10" />
                          <Input
                            ref={inputRef}
                            type="text"
                            placeholder={t('searchPlaceholder')}
                            className="pl-10 py-5 sm:py-6 text-base sm:text-lg rounded-lg sm:rounded-r-none relative z-0"
                            value={searchQuery}
                            onChange={(e) => {
                              const value = e.target.value
                              setSearchQuery(value)
                              
                              setIsDropdownOpen(true)
                              
                              if (value.trim().length >= 3) {
                                setShowTip(false)
                                debouncedFetch(value)
                              } else {
                                setShowTip(true)
                              }
                            }}
                            onFocus={() => {
                              if (searchQuery && searchQuery.trim().length >= 3) {
                                setShowTip(false)
                              } else {
                                setShowTip(true)
                              }
                            }}
                            autoComplete="off"
                            style={isSafari ? { WebkitAppearance: "none" } : undefined}
                          />
                        </div>
                        <Button 
                          type="submit" 
                          className="py-5 sm:py-6 px-4 sm:px-6 text-base sm:text-lg z-10 w-full sm:w-auto rounded-lg sm:rounded-l-none"
                          style={isSafari ? { WebkitAppearance: "none" } : undefined}
                        >
                          {t('searchButton')}
                        </Button>
                      </div>
                    </PopoverAnchor>

                    <PopoverContent 
                      className="w-[var(--radix-popover-trigger-width)] p-0" 
                      align="start"
                      sideOffset={5}
                      onOpenAutoFocus={(e: Event) => {
                        e.preventDefault()
                      }}
                      style={isSafari ? { WebkitOverflowScrolling: "touch" } : undefined}
                    >
                      <Command className="rounded-lg border shadow-md">
                        <div className="flex flex-col">
                          <div className="px-3 py-2 text-sm text-muted-foreground">
                            {t('selectChannelTip')}
                          </div>
                          <CommandList>
                            {showTip ? (
                              <div className="px-3 py-10 md:py-14 text-center">
                                <p className="text-sm text-muted-foreground">
                                  {t('minSearchChars')}
                                </p>
                              </div>
                            ) : suggestions.length === 0 && searchQuery.trim().length >= 3 ? (
                              <div className="px-3 py-10 md:py-14 text-center">
                                <h2 className="text-lg md:text-xl font-semibold mb-2 flex items-center justify-center gap-2">
                                  {t('noResults')} <span role="img" aria-label="confused">🤔</span>
                                </h2>
                                <p className="text-sm text-muted-foreground">
                                  {t('tryDifferentKeywords')}
                                </p>
                              </div>
                            ) : (
                              <>
                                {suggestions.map((suggestion) => (
                                  <CommandItem
                                    key={suggestion.channelId}
                                    onSelect={() => handleSelectSuggestion(suggestion)}
                                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors"
                                  >
                                    <Avatar className="h-10 w-10 flex-shrink-0">
                                      <AvatarImage src={suggestion.thumbnailUrl || "/images/placeholder.jpg"} />
                                      <AvatarFallback>{suggestion.name.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1 min-w-0">
                                      <p className="font-medium truncate">{suggestion.title}</p>
                                      <p className="text-sm text-muted-foreground truncate">
                                        {suggestion.customUrl}
                                      </p>
                                      <p className="text-sm text-muted-foreground truncate">
                                        {formatSubscribers(suggestion.subscriberCount)} {t('subscribers')}
                                      </p>
                                    </div>
                                  </CommandItem>
                                ))}
                              </>
                            )}
                          </CommandList>
                          {!showTip && suggestions.length > 0 && (
                            <div className="p-2 border-t">
                              <Button 
                                variant="secondary" 
                                className="w-full justify-center py-2 text-base"
                                onClick={handleSeeAllResults}
                              >
                                {t('viewAllResults')}
                              </Button>
                            </div>
                          )}
                        </div>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </form>
              </div>

              {/* 搜索结果列表 - 移动端优化 */}
              <div ref={resultsRef} className="mt-6 md:mt-8 safari-results-container">
                {isLoading ? (
                  <div className="max-w-4xl mx-auto flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : error ? (
                  <div className="max-w-4xl mx-auto py-8 text-center text-destructive">
                    {error}
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="max-w-4xl mx-auto">
                    <h2 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 px-2">{t('searchResults')}</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 px-2">
                      {searchResults.map((channel) => (
                        <Link
                          key={channel.channelId}
                          href={`/${locale}/channel/${encodeURIComponent(channel.channelId)}`}
                          className="block"
                        >
                          <Card className="h-full hover:shadow-lg transition-shadow">
                            <CardContent className="p-4 md:p-6">
                              <div className="flex flex-col items-center text-center">
                                <Avatar className="h-16 w-16 md:h-20 md:w-20 mb-3 md:mb-4">
                                  <AvatarImage src={channel.thumbnailUrl || "/images/placeholder.jpg"} />
                                  <AvatarFallback className="text-xl md:text-2xl">
                                    {channel.name.charAt(0)}
                                  </AvatarFallback>
                                </Avatar>
                                <h3 className="text-base md:text-lg font-semibold mb-2 line-clamp-2">{channel.title}</h3>
                                <div className="text-sm text-muted-foreground truncate">
                                  {channel.customUrl}
                                </div>
                                <div className="flex items-center gap-3 md:gap-4 text-xs md:text-sm text-muted-foreground">
                                  <div className="flex items-center">
                                    <Users className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1" />
                                    {formatSubscribers(channel.subscriberCount)}
                                  </div>
                                  <div className="flex items-center">
                                    <Play className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1" />
                                    {channel.videoCount || '0'} {t('videos')}
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : searchQuery.trim() ? (
                  <div className="max-w-4xl mx-auto">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 md:mb-8 px-2">
                      <h2 className="text-xl md:text-2xl font-bold">{t('searchResultsFor')} "{searchQuery}"</h2>
                      <div className="flex flex-wrap items-center gap-2 md:gap-4">
                        <Button 
                          variant="outline" 
                          className="flex items-center gap-2 text-sm h-9"
                          onClick={() => setIsTrackDialogOpen(true)}
                        >
                          <Plus className="h-3.5 w-3.5 md:h-4 md:w-4" />
                          <span className="hidden xs:inline">{t('addChannel')}</span>
                          <span className="xs:hidden">{t('add')}</span>
                        </Button>
                        <Button variant="outline" className="flex items-center gap-2 text-sm h-9">
                          <SlidersHorizontal className="h-3.5 w-3.5 md:h-4 md:w-4" />
                          <span className="hidden xs:inline">{t('filter')}</span>
                        </Button>
                        <Button variant="outline" className="flex items-center gap-2 text-sm h-9">
                          <span className="hidden xs:inline">{t('relevance')}</span>
                          <span className="xs:hidden">{t('sort')}</span>
                          <ChevronDown className="h-3.5 w-3.5 md:h-4 md:w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-center py-10 md:py-16">
                      <h3 className="text-lg md:text-xl font-semibold mb-2 flex items-center justify-center gap-2">
                        {t('noResults')} <span role="img" aria-label="confused">🤔</span>
                      </h3>
                      <p className="text-muted-foreground">
                        {t('tryDifferentKeywords')}
                      </p>
                    </div>
                  </div>
                ) : null}
              </div>

              
              {/* 使用 text-muted-foreground 替代 text-slate-500 */}
              {/* <div className="mt-4 text-muted-foreground text-sm">
                热门搜索:
                <button
                  // Add locale prefix to router push
                  onClick={() => router.push(`/${locale}/channel/TechChannel`)}
                  // 使用 text-primary 替代 text-emerald-600
                  className="mx-2 text-primary hover:underline"
                >
                  TechChannel
                </button>
                <button
                  // Add locale prefix to router push
                  onClick={() => router.push(`/${locale}/channel/GamingWorld`)}
                  // 使用 text-primary 替代 text-emerald-600
                  className="mx-2 text-primary hover:underline"
                >
                  GamingWorld
                </button>
                <button
                  // Add locale prefix to router push
                  onClick={() => router.push(`/${locale}/channel/FoodieNetwork`)}
                  // 使用 text-primary 替代 text-emerald-600
                  className="mx-2 text-primary hover:underline"
                >
                  FoodieNetwork
                </button>
              </div> */}
            </div>

            {/* Feature Cards - 移动端优化 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8 mt-10 md:mt-16 px-2 safari-card-grid">
              {/* 移除 border-0，让 Card 组件处理边框和背景 */}
              <Card className="shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="p-4 md:p-6">
                  {/* 使用 bg-primary/10 和 text-primary 替代 bg-emerald-100/text-emerald-600 */}
                  <div className="bg-primary/10 text-primary p-2 md:p-3 rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center mb-3 md:mb-4">
                    <TrendingUp className="h-5 w-5 md:h-6 md:w-6" />
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2">{t('feature1.title')}</h3>
                  {/* 使用 text-muted-foreground 替代 text-slate-600 */}
                  <p className="text-sm md:text-base text-muted-foreground">{t('feature1.description')}</p>
                </CardContent>
              </Card>

              {/* 移除 border-0 */}
              <Card className="shadow-md hover:shadow-lg transition-shadow">
                <CardContent className="p-4 md:p-6">
                   {/* 使用 bg-blue-500/10 和 text-blue-500 (或 theme.css 中定义的 chart-2 颜色) */}
                   {/* 这里暂时用 accent 举例，你可以根据需要调整 */}
                  <div className="bg-accent text-accent-foreground p-2 md:p-3 rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center mb-3 md:mb-4">
                    <BarChart3 className="h-5 w-5 md:h-6 md:w-6" />
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2">{t('feature2.title')}</h3>
                  {/* 使用 text-muted-foreground 替代 text-slate-600 */}
                  <p className="text-sm md:text-base text-muted-foreground">{t('feature2.description')}</p>
                </CardContent>
              </Card>

              {/* 移除 border-0 */}
              <Card className="shadow-md hover:shadow-lg transition-shadow sm:col-span-2 lg:col-span-1">
                <CardContent className="p-4 md:p-6">
                  {/* 使用 bg-purple-500/10 和 text-purple-500 (或 theme.css 中定义的 chart-x 颜色) */}
                  {/* 这里暂时用 secondary 举例 */}
                  <div className="bg-secondary text-secondary-foreground p-2 md:p-3 rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center mb-3 md:mb-4">
                    <Users className="h-5 w-5 md:h-6 md:w-6" />
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2">{t('feature3.title')}</h3>
                  {/* 使用 text-muted-foreground 替代 text-slate-600 */}
                  <p className="text-sm md:text-base text-muted-foreground">{t('feature3.description')}</p>
                </CardContent>
              </Card>
            </div>

            {/* Add a section to highlight the trend prediction feature */}
            {/* Add this after the Feature Cards section and before the CTA Section */}
            {/* <div className="mt-16 bg-slate-50 py-12 rounded-xl">
              <div className="max-w-5xl mx-auto px-4">
                <div className="text-center mb-8">
                  <Badge className="mb-2">新功能</Badge>
                  <h2 className="text-2xl md:text-3xl font-bold mb-3">趋势预测分析</h2>
                  <p className="text-slate-600 max-w-2xl mx-auto">
                    利用先进的AI算法，预测未来的频道表现趋势，帮助您提前做出内容和运营决策
                  </p>
                </div>
                <div className="bg-white shadow-lg rounded-lg overflow-hidden border">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">未来30天趋势预测示例</h3>
                      <Button variant="ghost" size="sm" className="text-emerald-600">
                        了解更多 <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                    <div className="h-64 bg-slate-100 rounded-lg"></div>
                  </div>
                </div>
              </div>
            </div> */}

            <Features />
            {/* CTA Section - 移动端优化 */}
            {/* <div className="mt-12 md:mt-20 text-center px-2 safari-cta">
              <h2 className="text-xl md:text-2xl font-bold mb-3 md:mb-4">{t('cta.title')}</h2>
              <p className="text-muted-foreground mb-5 md:mb-6">{t('cta.subtitle')}</p>
              <Button size="lg" className="w-full sm:w-auto px-6 md:px-8 py-2.5 md:py-3 text-base safari-cta-button" asChild
                style={{WebkitAppearance: "none"}} // Safari按钮样式修复
              >
                <Link href={`/${locale}/signup`}>
                  {t('cta.button')} <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div> */}
          </div>
        </section>

        {/* Track Channel Dialog - 移动端优化 */}
        <Dialog open={isTrackDialogOpen} onOpenChange={setIsTrackDialogOpen}>
          <DialogContent className="sm:max-w-[500px] max-w-[calc(100%-2rem)] p-4 sm:p-6 safari-dialog">
            <DialogHeader>
              <div className="flex items-center justify-between">
                <DialogTitle className="text-xl sm:text-2xl font-bold">{t('addChannelDialog.title')}</DialogTitle>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 p-0"
                  onClick={() => setIsTrackDialogOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <DialogDescription className="text-sm sm:text-base text-muted-foreground mt-2">
                {t('addChannelDialog.description')}
              </DialogDescription>
            </DialogHeader>
            <div className="py-3 sm:py-4">
              <Input
                placeholder={t('addChannelDialog.placeholder')}
                value={trackChannelInput}
                onChange={(e) => setTrackChannelInput(e.target.value)}
                className="w-full text-base safari-dialog-input"
                disabled={isSubmitting}
                style={{WebkitAppearance: "none"}} // Safari输入框样式修复
              />
            </div>
            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0 safari-dialog-footer">
              <Button
                variant="ghost"
                onClick={() => setIsTrackDialogOpen(false)}
                disabled={isSubmitting}
                className="w-full sm:w-auto"
              >
                {t('cancel')}
              </Button>
              <Button
                onClick={handleTrackChannel}
                disabled={!trackChannelInput.trim() || isSubmitting}
                className="w-full sm:w-auto"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('processing')}
                  </>
                ) : (
                  t('submit')
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <Toaster />
    </>
  )
}
